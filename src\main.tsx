import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import ReactDOM from "react-dom/client";
import "./index.css";
import App from "./App";

// createRoot(document.getElementById("root")!).render(
//   // <StrictMode>
//     <App />
//   // </StrictMode>
// );
const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
  <>
    {/* <ConfigProvider theme={theme}> */}
    <App />
    {/* </ConfigProvider> */}
  </>
);
