import React from 'react';
import { Navigate } from 'react-router-dom';
import Login from "../components/Auth/Login";
import Dashboard from "../components/Dashboard/Dashboard";
import Layout from "../components/Layout/Layout";
import PageBuilder from "../components/Pages/PageBuilder";
import TemplateManager from "../components/Templates/TemplateManager";
import UserManager from "../components/Users/<USER>";
import WebsiteManager from "../components/Websites/WebsiteManager";

export const ROUTES = {
    dashboard: "/",

};

export const appRoot = "/";

const LOGIN_ROUTES = [
    {
        index: true,
        element: <Login />,
    },
];


const ALL_ROUTES = (appProps) => [
    ...LOGIN_ROUTES,

    {
        path: `${appRoot}`,
        element: <Layout {...appProps} />,
        children: [
            {
                index: true,
                element: <Dashboard to={`${appRoot}${ROUTES.dashboard}`} {...appProps} />,
            },
            {
                path: "pages", // Child route for /service/:id/log
                element: <PageBuilder {...appProps} />,
            },
            {
                path: "templates", // Child route for /service/:id/log
                element: <TemplateManager {...appProps} />,
            },
            {
                path: "websites", // Child route for /service/:id/log
                element: <WebsiteManager {...appProps} />,
            },
            {
                path: "users", // Child route for /service/:id/log
                element: <UserManager {...appProps} />,
                children: [
                ],
            },

            // {
            //   // index: true,
            //   path: "logAnalytic",
            //   element: <LogAnalytic {...appProps} />,
            // },
        ],
    },

    //   {
    //     path: "/error",
    //     element: <Error />,
    //   },
    //   {
    //     path: "/unAuthorize",
    //     element: <UnAuthorize />,
    //   },
    //   {
    //     path: "/noInternet",
    //     element: <NoInternet />,
    //   },
    //   {
    //     path: "/blockuser",
    //     element: <BlockUser />,
    //   },
    //   {
    //     path: "/suspendeduser",
    //     element: <SuspendentUser />,
    //   },
    //   {
    //     path: "/undermaintenance",
    //     element: <MaintenancePage />,
    //   },
    {
        path: "*",
        element: <Navigate to="/error" />,
    },
];

export default ALL_ROUTES;