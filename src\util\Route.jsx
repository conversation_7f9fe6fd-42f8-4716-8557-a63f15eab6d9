import React from "react";
import { Navigate } from "react-router-dom";
import Login from "../components/Auth/Login";
import Dashboard from "../components/Dashboard/Dashboard";
import Layout from "../components/Layout/Layout";
import PageBuilder from "../components/Pages/PageBuilder";
import TemplateManager from "../components/Templates/TemplateManager";
import UserManager from "../components/Users/<USER>";
import WebsiteManager from "../components/Websites/WebsiteManager";
import { AuthProvider, useAuth } from "../contexts/AuthContext";
import ComponentManager from "../components/Components/ComponentManager";
import CategoryManager from "../components/Categories/CategoryManager";

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="tw-flex tw-items-center tw-justify-center tw-min-h-screen">
        <div className="tw-text-lg">Loading...</div>
      </div>
    );
  }

  return user ? <>{children}</> : <Navigate to="/login" replace />;
};

export const ROUTES = {
  dashboard: "/",
};

const LOGIN_ROUTES = [
  {
    index: true,
    element: (
      // <AuthProvider>
      <Login />
      // </AuthProvider>
    ),
  },
];

export const appRoot = "/";

export const ROLES = {
  ADMIN: "admin",
  CONTENT: "content_team",
  DEFAULT: "default",
};

const ALL_ROUTES = (appProps) => [
  // Login route (separate from protected routes)
  ...LOGIN_ROUTES,
  // {
  //   path: "/login",
  //   element: <Login />,
  // },

  // Protected routes with Layout
  {
    path: "/",
    element: (
      // <AuthProvider>
      <Layout {...appProps} />
      // </AuthProvider>
    ),
    // element: <Layout {...appProps} />,
    Role: [ROLES.ADMIN, ROLES.CONTENT, ROLES.DEFAULT],
    children: [
      {
        index: true,
        element: <Navigate to={`/`} {...appProps} appProps={appProps} />,
      },
      {
        path: "/",
        element: <Dashboard {...appProps} />,
        Role: [ROLES.ADMIN, ROLES.CONTENT, ROLES.DEFAULT],
      },
      {
        path: "/pages",
        element: <PageBuilder {...appProps} />,
        Role: [ROLES.ADMIN, ROLES.CONTENT, ROLES.DEFAULT],
      },
      {
        path: "/categories",
        element: <CategoryManager {...appProps} />,
        Role: [ROLES.ADMIN, ROLES.CONTENT, ROLES.DEFAULT],
      },
      {
        path: "/components",
        element: <ComponentManager {...appProps} />,
        Role: [ROLES.ADMIN, ROLES.CONTENT, ROLES.DEFAULT],
      },
      {
        path: "/templates",
        element: <TemplateManager {...appProps} />,
        Role: [ROLES.ADMIN, ROLES.CONTENT, ROLES.DEFAULT],
      },
      {
        path: "/websites",
        element: <WebsiteManager {...appProps} />,
        Role: [ROLES.ADMIN, ROLES.CONTENT, ROLES.DEFAULT],
      },
      {
        path: "/users",
        element: <UserManager {...appProps} />,
        Role: [ROLES.ADMIN, ROLES.CONTENT, ROLES.DEFAULT],
      },
    ],
  },

  // Fallback route
  // {
  //   path: "*",
  //   element: <Navigate to="/login" replace />,
  // },
];

export default ALL_ROUTES;
