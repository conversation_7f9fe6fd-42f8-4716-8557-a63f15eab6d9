import React, { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
  RouterProvider,
  createBrowserRouter,
} from "react-router-dom";
import Login from "./components/Auth/Login";
import Dashboard from "./components/Dashboard/Dashboard";
import CategoryManager from "./components/Categories/CategoryManager";
import ComponentManager from "./components/Components/ComponentManager";
import PageBuilder from "./components/Pages/PageBuilder";
import TemplateManager from "./components/Templates/TemplateManager";
import WebsiteManager from "./components/Websites/WebsiteManager";
import UserManager from "./components/Users/<USER>";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import ALL_ROUTES from "./util/Route";
import { generateRoutes } from "./util/functions";

function App() {
  //   const [profile, SetProfile] = useLocalStorage("profile", {
  //   role: ROLES.DEFAULT,
  // });
  const router = createBrowserRouter(generateRoutes(ALL_ROUTES({}), "default"));
  return (
    <AuthProvider>
      <RouterProvider router={router} />
    </AuthProvider>
  );
}

function PrivateRoute({ children }) {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center">
        <div className="tw-animate-spin tw-rounded-full tw-h-32 tw-w-32 tw-border-b-2 tw-border-blue-600"></div>
      </div>
    );
  }

  return user ? children : <Navigate to="/login" />;
}

function AdminRoute({ children }) {
  const { user } = useAuth();
  return user && user.role === "admin" ? children : <Navigate to="/" />;
}

export default App;
