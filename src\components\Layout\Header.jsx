import React from "react";
import { useAuth } from "../../contexts/AuthContext";
import { Bell, Search, Settings } from "lucide-react";
import { Avatar } from "antd";

const Header = ({ title, subtitle }) => {
  const { user } = useAuth();

  return (
    <div className="tw-bg-white tw-shadow-sm tw-border-b tw-border-gray-200 tw-px-6 tw-py-3">
      <div className="tw-flex tw-items-center tw-justify-between">
        <div>
          <h1 className="!tw-text-2xl tw-font-bold tw-text-gray-900">
            {title}
          </h1>
          {subtitle && (
            <p className="tw-text-sm tw-text-gray-600 tw-mt-1">{subtitle}</p>
          )}
        </div>

        <div className="tw-flex tw-items-center tw-space-x-4">
          <Avatar className="!tw-bg-[#374151]" size={40}>
            {user?.username?.charAt(0).toUpperCase()}
          </Avatar>
        </div>
      </div>
    </div>
  );
};

export default Header;
