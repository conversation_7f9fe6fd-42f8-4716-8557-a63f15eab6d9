import {
  BrowserRouter as Router,
  Navigate,
  Router<PERSON>rovider,
  createBrowserRouter,
} from "react-router-dom";
// import Dashboard from "./components/Dashboard/Dashboard";
// Temporarily disabled due to JSX syntax errors
// import CategoryManager from "./components/Categories/CategoryManager";
// import ComponentManager from "./components/Components/ComponentManager";
// import PageBuilder from "./components/Pages/PageBuilder";
// import TemplateManager from "./components/Templates/TemplateManager";
// import WebsiteManager from "./components/Websites/WebsiteManager";
// import UserManager from "./components/Users/<USER>";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { SidebarProvider } from "./contexts/SidebarContext";
import Layout from "./components/Layout/Layout";
// import LoginPage from "./components/Auth/Login";
import ALL_ROUTES from "./util/Route";

function App() {
  const router = createBrowserRouter(
    ALL_ROUTES({
      // socket,
      // systems_status: systemsStatus,
      // set_production_mode: setProductionMode,
      // production_mode: productionMode,
      // loading,
      // user_details: userDetails,
      // is_loading: API.isLoading,
      // plan,
      // error_status: errorStatus,
      // update_profile: updateProfile,
      // theme: themes,
      // setRefresh: setRefresh,
      // maintenance: maintenance,
      // isMaintenance: isMaintenance,
    })
  );

  return (
    <AuthProvider>
      <SidebarProvider>
        <RouterProvider router={router} />
        {/* <Router>
          <div className="tw-min-h-screen tw-bg-gray-50">
            <Routes>
              <Route path="/login" element={<LoginPage />} />
              <Route
                path="/"
                element={
                  <PrivateRoute>
                    <Dashboard />
                  </PrivateRoute>
                }
              />
              <Route
                path="/categories"
                element={
                  <PrivateRoute>
                    <CategoryManager />
                  </PrivateRoute>
                }
              />
              <Route
                path="/components"
                element={
                  <PrivateRoute>
                    <ComponentManager />
                  </PrivateRoute>
                }
              />
              <Route
                path="/pages"
                element={
                  <PrivateRoute>
                    <PageBuilder />
                  </PrivateRoute>
                }
              />
              <Route
                path="/templates"
                element={
                  <PrivateRoute>
                    <TemplateManager />
                  </PrivateRoute>
                }
              />
              <Route
                path="/websites"
                element={
                  <PrivateRoute>
                    <WebsiteManager />
                  </PrivateRoute>
                }
              />
              <Route
                path="/users"
                element={
                  <AdminRoute>
                    <UserManager />
                  </AdminRoute>
                }
              />
            </Routes>
          </div>
        </Router> */}
      </SidebarProvider>
    </AuthProvider>
  );
}

function PrivateRoute({ children }) {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center">
        <div className="tw-animate-spin tw-rounded-full tw-h-32 tw-w-32 tw-border-b-2 tw-border-blue-600"></div>
      </div>
    );
  }

  return user ? <Layout>{children}</Layout> : <Navigate to="/login" />;
}

function AdminRoute({ children }) {
  const { user } = useAuth();
  return user && user.role === "admin" ? (
    <Layout>{children}</Layout>
  ) : (
    <Navigate to="/" />
  );
}

export default App;
